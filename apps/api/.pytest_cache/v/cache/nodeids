["tests/test_bills_api.py::TestBillsAPI::test_create_bill", "tests/test_bills_api.py::TestBillsAPI::test_create_bill_with_duplicate_external_id", "tests/test_bills_api.py::TestBillsAPI::test_create_bill_with_tags_and_categories", "tests/test_bills_api.py::TestBillsAPI::test_delete_bill", "tests/test_bills_api.py::TestBillsAPI::test_delete_bill_not_found", "tests/test_bills_api.py::TestBillsAPI::test_external_id_not_found", "tests/test_bills_api.py::TestBillsAPI::test_get_bill_by_external_id", "tests/test_bills_api.py::TestBillsAPI::test_get_bill_not_found", "tests/test_bills_api.py::TestBillsAPI::test_get_bills_by_session_year", "tests/test_bills_api.py::TestBillsAPI::test_get_bills_by_sponsor", "tests/test_bills_api.py::TestBillsAPI::test_get_bills_by_status_endpoint", "tests/test_bills_api.py::TestBillsAPI::test_get_bills_by_type_endpoint", "tests/test_bills_api.py::TestBillsAPI::test_get_bills_count", "tests/test_bills_api.py::TestBillsAPI::test_get_bills_empty", "tests/test_bills_api.py::TestBillsAPI::test_get_featured_bills", "tests/test_bills_api.py::TestBillsAPI::test_invalid_external_id_type", "tests/test_bills_api.py::TestBillsAPI::test_pagination", "tests/test_bills_api.py::TestBillsAPI::test_search_bills_by_status", "tests/test_bills_api.py::TestBillsAPI::test_search_bills_by_title", "tests/test_bills_api.py::TestBillsAPI::test_update_bill", "tests/test_bills_api.py::TestBillsAPI::test_update_bill_not_found", "tests/test_campaigns_api.py::TestCampaignsAPI::test_create_campaign", "tests/test_campaigns_api.py::TestCampaignsAPI::test_create_campaign_invalid_bill", "tests/test_campaigns_api.py::TestCampaignsAPI::test_create_campaign_with_json_fields", "tests/test_campaigns_api.py::TestCampaignsAPI::test_delete_campaign", "tests/test_campaigns_api.py::TestCampaignsAPI::test_delete_campaign_not_found", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_active_campaigns", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_campaign", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_campaign_not_found", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_campaigns_by_bill", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_campaigns_by_status", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_campaigns_empty", "tests/test_campaigns_api.py::TestCampaignsAPI::test_get_featured_campaigns", "tests/test_campaigns_api.py::TestCampaignsAPI::test_pagination", "tests/test_campaigns_api.py::TestCampaignsAPI::test_search_campaigns", "tests/test_campaigns_api.py::TestCampaignsAPI::test_update_campaign", "tests/test_campaigns_api.py::TestCampaignsAPI::test_update_campaign_not_found", "tests/test_campaigns_api.py::TestCampaignsAPI::test_update_campaign_with_json_fields", "tests/test_config.py::TestConfiguration::test_api_key_settings", "tests/test_config.py::TestConfiguration::test_boolean_settings", "tests/test_config.py::TestConfiguration::test_case_sensitivity", "tests/test_config.py::TestConfiguration::test_cors_origins_list", "tests/test_config.py::TestConfiguration::test_database_url_setting", "tests/test_config.py::TestConfiguration::test_default_settings", "tests/test_config.py::TestConfiguration::test_environment_variable_override", "tests/test_config.py::TestConfiguration::test_global_settings_instance", "tests/test_config.py::TestConfiguration::test_integer_settings", "tests/test_config.py::TestConfiguration::test_optional_settings", "tests/test_config.py::TestConfiguration::test_production_settings", "tests/test_config.py::TestConfiguration::test_redis_url_setting", "tests/test_config.py::TestConfiguration::test_ses_settings", "tests/test_database.py::TestDatabaseConnection::test_database_connection", "tests/test_database.py::TestDatabaseConnection::test_database_foreign_key_constraints", "tests/test_database.py::TestDatabaseConnection::test_database_rollback", "tests/test_database.py::TestDatabaseConnection::test_database_session_isolation", "tests/test_database.py::TestDatabaseConnection::test_database_table_creation", "tests/test_database.py::TestDatabaseConnection::test_database_transaction", "tests/test_health.py::test_health_check", "tests/test_health.py::test_health_check_cors", "tests/test_health.py::test_health_check_headers", "tests/test_health.py::test_health_check_with_test_client", "tests/test_models.py::TestActionModel::test_action_properties", "tests/test_models.py::TestActionModel::test_create_action", "tests/test_models.py::TestBillModel::test_bill_enum_values", "tests/test_models.py::TestBillModel::test_bill_full_id_property", "tests/test_models.py::TestBillModel::test_create_bill", "tests/test_models.py::TestCampaignModel::test_campaign_completion_percentage", "tests/test_models.py::TestCampaignModel::test_campaign_properties", "tests/test_models.py::TestCampaignModel::test_create_campaign", "tests/test_models.py::TestModelRelationships::test_bill_campaigns_relationship", "tests/test_models.py::TestModelRelationships::test_user_actions_relationship", "tests/test_models.py::TestOfficialModel::test_create_official", "tests/test_models.py::TestOfficialModel::test_official_contact_preference", "tests/test_models.py::TestOfficialModel::test_official_properties", "tests/test_models.py::TestUserModel::test_create_user", "tests/test_models.py::TestUserModel::test_user_full_name_property", "tests/test_models.py::TestUserModel::test_user_unique_email", "tests/test_officials_api.py::TestOfficialsAPI::test_create_official", "tests/test_officials_api.py::TestOfficialsAPI::test_create_official_duplicate_external_id", "tests/test_officials_api.py::TestOfficialsAPI::test_delete_official", "tests/test_officials_api.py::TestOfficialsAPI::test_get_official_by_external_id", "tests/test_officials_api.py::TestOfficialsAPI::test_get_official_by_id", "tests/test_officials_api.py::TestOfficialsAPI::test_get_official_not_found", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_by_chamber_endpoint", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_by_level_endpoint", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_by_zip_code", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_empty", "tests/test_officials_api.py::TestOfficialsAPI::test_invalid_zip_code", "tests/test_officials_api.py::TestOfficialsAPI::test_pagination", "tests/test_officials_api.py::TestOfficialsAPI::test_search_officials_by_level", "tests/test_officials_api.py::TestOfficialsAPI::test_search_officials_by_name", "tests/test_officials_api.py::TestOfficialsAPI::test_update_official"]